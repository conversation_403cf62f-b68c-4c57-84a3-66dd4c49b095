<!doctype html>
<html lang="en" data-theme="light" class="light">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="color-scheme" content="light" />
    <title>Element Plus Vite Starter</title>
    <!-- element css cdn, if you use custom theme, remove it. -->
    <!-- <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/element-plus/dist/index.css"
    /> -->
  </head>
  <body class="light" data-theme="light">
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
