$--colors: (
  'primary': (
    'base': #1890ff,
  ),
  'success': (
    'base': #52c41a,
  ),
  'warning': (
    'base': #faad14,
  ),
  'danger': (
    'base': #ff4d4f,
  ),
  'error': (
    'base': #ff4d4f,
  ),
  'info': (
    'base': #1890ff,
  ),
);

// we can add this to custom namespace, default is 'el'
@forward 'element-plus/theme-chalk/src/mixins/config.scss' with (
  $namespace: 'ep'
);

// You should use them in scss, because we calculate it by sass.
// comment next lines to use default color
@forward 'element-plus/theme-chalk/src/common/var.scss' with (
  // do not use same name, it will override.
  $colors: $--colors,
  $button-padding-horizontal: ('default': 50px)
);

// if you want to import all
// @use "element-plus/theme-chalk/src/index.scss" as *;

// You can comment it to hide debug info.
// @debug $--colors;

// custom dark variables
@use './dark.scss';
