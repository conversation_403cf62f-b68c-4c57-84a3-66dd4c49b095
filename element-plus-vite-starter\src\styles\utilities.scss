// ===== 原子化 CSS 工具类 =====

// ===== 布局工具类 =====

// Flexbox
.flex { display: flex; }
.inline-flex { display: inline-flex; }

// Flex Direction
.flex-row { flex-direction: row; }
.flex-row-reverse { flex-direction: row-reverse; }
.flex-col { flex-direction: column; }
.flex-col-reverse { flex-direction: column-reverse; }

// Flex Wrap
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }
.flex-wrap-reverse { flex-wrap: wrap-reverse; }

// Flex
.flex-1 { flex: 1 1 0%; }
.flex-auto { flex: 1 1 auto; }
.flex-initial { flex: 0 1 auto; }
.flex-none { flex: none; }

// Flex Grow
.flex-grow { flex-grow: 1; }
.flex-grow-0 { flex-grow: 0; }

// Flex Shrink
.flex-shrink { flex-shrink: 1; }
.flex-shrink-0 { flex-shrink: 0; }

// Justify Content
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.justify-evenly { justify-content: space-evenly; }

// Align Items
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.items-center { align-items: center; }
.items-baseline { align-items: baseline; }
.items-stretch { align-items: stretch; }

// Align Content
.content-start { align-content: flex-start; }
.content-end { align-content: flex-end; }
.content-center { align-content: center; }
.content-between { align-content: space-between; }
.content-around { align-content: space-around; }
.content-evenly { align-content: space-evenly; }

// Align Self
.self-auto { align-self: auto; }
.self-start { align-self: flex-start; }
.self-end { align-self: flex-end; }
.self-center { align-self: center; }
.self-stretch { align-self: stretch; }
.self-baseline { align-self: baseline; }

// Grid
.grid { display: grid; }
.inline-grid { display: inline-grid; }

// Grid Template Columns
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
.grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
.grid-cols-12 { grid-template-columns: repeat(12, minmax(0, 1fr)); }

// Gap
.gap-0 { gap: 0; }
.gap-1 { gap: var(--spacing-xs); }
.gap-2 { gap: var(--spacing-sm); }
.gap-3 { gap: var(--spacing-md); }
.gap-4 { gap: var(--spacing-lg); }
.gap-5 { gap: var(--spacing-xl); }
.gap-6 { gap: var(--spacing-xxl); }
.gap-8 { gap: var(--spacing-xxxl); }

// ===== 间距工具类 =====

// Margin
.m-0 { margin: 0; }
.m-1 { margin: var(--spacing-xs); }
.m-2 { margin: var(--spacing-sm); }
.m-3 { margin: var(--spacing-md); }
.m-4 { margin: var(--spacing-lg); }
.m-5 { margin: var(--spacing-xl); }
.m-6 { margin: var(--spacing-xxl); }
.m-8 { margin: var(--spacing-xxxl); }
.m-auto { margin: auto; }

// Margin X
.mx-0 { margin-left: 0; margin-right: 0; }
.mx-1 { margin-left: var(--spacing-xs); margin-right: var(--spacing-xs); }
.mx-2 { margin-left: var(--spacing-sm); margin-right: var(--spacing-sm); }
.mx-3 { margin-left: var(--spacing-md); margin-right: var(--spacing-md); }
.mx-4 { margin-left: var(--spacing-lg); margin-right: var(--spacing-lg); }
.mx-5 { margin-left: var(--spacing-xl); margin-right: var(--spacing-xl); }
.mx-6 { margin-left: var(--spacing-xxl); margin-right: var(--spacing-xxl); }
.mx-8 { margin-left: var(--spacing-xxxl); margin-right: var(--spacing-xxxl); }
.mx-auto { margin-left: auto; margin-right: auto; }

// Margin Y
.my-0 { margin-top: 0; margin-bottom: 0; }
.my-1 { margin-top: var(--spacing-xs); margin-bottom: var(--spacing-xs); }
.my-2 { margin-top: var(--spacing-sm); margin-bottom: var(--spacing-sm); }
.my-3 { margin-top: var(--spacing-md); margin-bottom: var(--spacing-md); }
.my-4 { margin-top: var(--spacing-lg); margin-bottom: var(--spacing-lg); }
.my-5 { margin-top: var(--spacing-xl); margin-bottom: var(--spacing-xl); }
.my-6 { margin-top: var(--spacing-xxl); margin-bottom: var(--spacing-xxl); }
.my-8 { margin-top: var(--spacing-xxxl); margin-bottom: var(--spacing-xxxl); }
.my-auto { margin-top: auto; margin-bottom: auto; }

// Margin Top
.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }
.mt-5 { margin-top: var(--spacing-xl); }
.mt-6 { margin-top: var(--spacing-xxl); }
.mt-8 { margin-top: var(--spacing-xxxl); }
.mt-auto { margin-top: auto; }

// Margin Right
.mr-0 { margin-right: 0; }
.mr-1 { margin-right: var(--spacing-xs); }
.mr-2 { margin-right: var(--spacing-sm); }
.mr-3 { margin-right: var(--spacing-md); }
.mr-4 { margin-right: var(--spacing-lg); }
.mr-5 { margin-right: var(--spacing-xl); }
.mr-6 { margin-right: var(--spacing-xxl); }
.mr-8 { margin-right: var(--spacing-xxxl); }
.mr-auto { margin-right: auto; }

// Margin Bottom
.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }
.mb-6 { margin-bottom: var(--spacing-xxl); }
.mb-8 { margin-bottom: var(--spacing-xxxl); }
.mb-auto { margin-bottom: auto; }

// Margin Left
.ml-0 { margin-left: 0; }
.ml-1 { margin-left: var(--spacing-xs); }
.ml-2 { margin-left: var(--spacing-sm); }
.ml-3 { margin-left: var(--spacing-md); }
.ml-4 { margin-left: var(--spacing-lg); }
.ml-5 { margin-left: var(--spacing-xl); }
.ml-6 { margin-left: var(--spacing-xxl); }
.ml-8 { margin-left: var(--spacing-xxxl); }
.ml-auto { margin-left: auto; }

// Padding
.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-xs); }
.p-2 { padding: var(--spacing-sm); }
.p-3 { padding: var(--spacing-md); }
.p-4 { padding: var(--spacing-lg); }
.p-5 { padding: var(--spacing-xl); }
.p-6 { padding: var(--spacing-xxl); }
.p-8 { padding: var(--spacing-xxxl); }

// Padding X
.px-0 { padding-left: 0; padding-right: 0; }
.px-1 { padding-left: var(--spacing-xs); padding-right: var(--spacing-xs); }
.px-2 { padding-left: var(--spacing-sm); padding-right: var(--spacing-sm); }
.px-3 { padding-left: var(--spacing-md); padding-right: var(--spacing-md); }
.px-4 { padding-left: var(--spacing-lg); padding-right: var(--spacing-lg); }
.px-5 { padding-left: var(--spacing-xl); padding-right: var(--spacing-xl); }
.px-6 { padding-left: var(--spacing-xxl); padding-right: var(--spacing-xxl); }
.px-8 { padding-left: var(--spacing-xxxl); padding-right: var(--spacing-xxxl); }

// Padding Y
.py-0 { padding-top: 0; padding-bottom: 0; }
.py-1 { padding-top: var(--spacing-xs); padding-bottom: var(--spacing-xs); }
.py-2 { padding-top: var(--spacing-sm); padding-bottom: var(--spacing-sm); }
.py-3 { padding-top: var(--spacing-md); padding-bottom: var(--spacing-md); }
.py-4 { padding-top: var(--spacing-lg); padding-bottom: var(--spacing-lg); }
.py-5 { padding-top: var(--spacing-xl); padding-bottom: var(--spacing-xl); }
.py-6 { padding-top: var(--spacing-xxl); padding-bottom: var(--spacing-xxl); }
.py-8 { padding-top: var(--spacing-xxxl); padding-bottom: var(--spacing-xxxl); }

// ===== 尺寸工具类 =====

// Width
.w-0 { width: 0; }
.w-full { width: 100%; }
.w-screen { width: 100vw; }
.w-auto { width: auto; }
.w-fit { width: fit-content; }
.w-min { width: min-content; }
.w-max { width: max-content; }

// Height
.h-0 { height: 0; }
.h-full { height: 100%; }
.h-screen { height: 100vh; }
.h-auto { height: auto; }
.h-fit { height: fit-content; }
.h-min { height: min-content; }
.h-max { height: max-content; }

// ===== 文本工具类 =====

// Text Align
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-justify { text-align: justify; }

// Font Size
.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-md { font-size: var(--font-size-md); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-xxl); }
.text-3xl { font-size: var(--font-size-xxxl); }

// Font Weight
.font-light { font-weight: var(--font-weight-light); }
.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }

// Line Height
.leading-none { line-height: 1; }
.leading-tight { line-height: var(--line-height-xs); }
.leading-snug { line-height: var(--line-height-sm); }
.leading-normal { line-height: var(--line-height-base); }
.leading-relaxed { line-height: var(--line-height-lg); }
.leading-loose { line-height: var(--line-height-xl); }

// Text Color
.text-primary { color: var(--color-text-primary); }
.text-regular { color: var(--color-text-regular); }
.text-secondary { color: var(--color-text-secondary); }
.text-placeholder { color: var(--color-text-placeholder); }
.text-disabled { color: var(--color-text-disabled); }
.text-white { color: var(--color-white); }
.text-black { color: var(--color-black); }

// ===== 背景工具类 =====

// Background Color
.bg-white { background-color: var(--color-white); }
.bg-black { background-color: var(--color-black); }
.bg-primary { background-color: var(--color-primary); }
.bg-success { background-color: var(--color-success); }
.bg-warning { background-color: var(--color-warning); }
.bg-danger { background-color: var(--color-danger); }
.bg-info { background-color: var(--color-info); }
.bg-transparent { background-color: transparent; }

// Glass Effect
.bg-glass { 
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
}

.bg-glass-light { 
  background: var(--glass-bg-light);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
}

.bg-glass-dark { 
  background: var(--glass-bg-dark);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
}

// ===== 边框工具类 =====

// Border Radius
.rounded-none { border-radius: 0; }
.rounded-xs { border-radius: var(--border-radius-xs); }
.rounded-sm { border-radius: var(--border-radius-sm); }
.rounded { border-radius: var(--border-radius-md); }
.rounded-md { border-radius: var(--border-radius-md); }
.rounded-lg { border-radius: var(--border-radius-lg); }
.rounded-xl { border-radius: var(--border-radius-xl); }
.rounded-2xl { border-radius: var(--border-radius-xxl); }
.rounded-full { border-radius: var(--border-radius-round); }

// ===== 阴影工具类 =====

.shadow-none { box-shadow: none; }
.shadow-sm { box-shadow: var(--shadow-base); }
.shadow { box-shadow: var(--shadow-light); }
.shadow-md { box-shadow: var(--shadow-dark); }
.shadow-lg { box-shadow: var(--shadow-heavy); }
.shadow-glass { box-shadow: var(--glass-shadow); }

// ===== 过渡工具类 =====

.transition-none { transition: none; }
.transition-all { transition: var(--transition-all); }
.transition-colors { transition: var(--transition-color), var(--transition-background), var(--transition-border); }
.transition-opacity { transition: var(--transition-fade); }
.transition-shadow { transition: var(--transition-box-shadow); }
.transition-transform { transition: transform var(--transition-duration-base) var(--transition-ease-in-out); }

// ===== 显示工具类 =====

.block { display: block; }
.inline-block { display: inline-block; }
.inline { display: inline; }
.hidden { display: none; }

// ===== 位置工具类 =====

.static { position: static; }
.fixed { position: fixed; }
.absolute { position: absolute; }
.relative { position: relative; }
.sticky { position: sticky; }

// ===== 溢出工具类 =====

.overflow-auto { overflow: auto; }
.overflow-hidden { overflow: hidden; }
.overflow-visible { overflow: visible; }
.overflow-scroll { overflow: scroll; }
.overflow-x-auto { overflow-x: auto; }
.overflow-y-auto { overflow-y: auto; }
.overflow-x-hidden { overflow-x: hidden; }
.overflow-y-hidden { overflow-y: hidden; }

// ===== 光标工具类 =====

.cursor-auto { cursor: auto; }
.cursor-default { cursor: default; }
.cursor-pointer { cursor: pointer; }
.cursor-wait { cursor: wait; }
.cursor-text { cursor: text; }
.cursor-move { cursor: move; }
.cursor-help { cursor: help; }
.cursor-not-allowed { cursor: not-allowed; }
