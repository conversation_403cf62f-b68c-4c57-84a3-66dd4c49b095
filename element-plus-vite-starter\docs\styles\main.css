/* 现代化API文档样式 - 主文件 */

/* 导入所有模块化的CSS文件 */
@import url('./base.css');
@import url('./utilities.css');
@import url('./performance.css');
@import url('./buttons.css');
@import url('./layout.css');
@import url('./components.css');
@import url('./pages.css');
@import url('./accounts.css');
@import url('./api-modal.css');
@import url('./config.css');
@import url('./add-account.css');
@import url('./qrcode.css');
@import url('./animations.css');
@import url('./responsive.css');

/*
文件结构说明：
- base.css: 基础样式（CSS变量、重置、字体系统）
- utilities.css: CSS工具类（间距、文本、显示、Flexbox等）
- performance.css: 性能优化样式（GPU加速、减少重绘等）
- layout.css: 布局相关（头部、侧边栏、内容区域）
- components.css: 通用组件（按钮、模态框、表单等）
- pages.css: 页面特定样式（概览、API文档等）
- accounts.css: 账号相关样式
- api-modal.css: API详情模态框样式
- config.css: 配置管理页面样式
- add-account.css: 添加账号功能样式
- qrcode.css: 二维码相关样式
- animations.css: 动画和骨架屏样式
- responsive.css: 响应式设计

优化特性：
✅ 模块化架构 - 便于维护和按需加载
✅ 设计令牌系统 - 统一的颜色、间距、字体变量
✅ CSS工具类 - 快速开发和原型设计
✅ 性能优化 - GPU加速、减少重绘、懒加载
✅ 响应式设计 - 移动端优先，多断点适配
✅ 可访问性 - 支持减少动画、高对比度模式
✅ 暗色模式准备 - 预设暗色主题变量
*/
